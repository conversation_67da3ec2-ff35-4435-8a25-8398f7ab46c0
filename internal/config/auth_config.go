package config

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"time"
)

type AuthConfig struct {
	JWT         JWTConfig
	OAuth       OAuthConfig
	Domain      string
	FrontendURL string
}

type JWTConfig struct {
	Secret     string
	Expiration time.Duration
	Issuer     string
}

type OAuthConfig struct {
	GitHub   OAuthProvider
	Google   OAuthProvider
	LinkedIn OAuthProvider
}

type OAuthProvider struct {
	ClientID     string
	ClientSecret string
}

func NewAuthConfig() *AuthConfig {
	return &AuthConfig{
		JWT: JWTConfig{
			Secret:     generateJWTSecret(),
			Expiration: 24 * time.Hour,
			Issuer:     "impactresume",
		},
		Domain:      "localhost:8080",
		FrontendURL: "http://localhost:5173",
	}
}

func generateJWTSecret() string {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		panic(fmt.Sprintf("failed to generate JWT secret: %v", err))
	}
	return base64.URLEncoding.EncodeToString(bytes)
}

// AuthSecrets represents the structure of authentication secrets
type AuthSecrets struct {
	JWTSecret            string `json:"jwt_secret"`
	GitHubClientID       string `json:"github_client_id"`
	GitHubClientSecret   string `json:"github_client_secret"`
	GoogleClientID       string `json:"google_client_id"`
	GoogleClientSecret   string `json:"google_client_secret"`
	LinkedInClientID     string `json:"linkedin_client_id"`
	LinkedInClientSecret string `json:"linkedin_client_secret"`
	Domain               string `json:"domain"`
	FrontendURL          string `json:"frontend_url"`
}

// SetFromSecrets sets authentication configuration from AWS Secrets Manager secrets
func (ac *AuthConfig) SetFromSecrets(secrets *AuthSecrets) {
	if secrets.JWTSecret != "" {
		ac.JWT.Secret = secrets.JWTSecret
	}

	if secrets.GitHubClientID != "" {
		ac.OAuth.GitHub.ClientID = secrets.GitHubClientID
	}
	if secrets.GitHubClientSecret != "" {
		ac.OAuth.GitHub.ClientSecret = secrets.GitHubClientSecret
	}

	if secrets.GoogleClientID != "" {
		ac.OAuth.Google.ClientID = secrets.GoogleClientID
	}
	if secrets.GoogleClientSecret != "" {
		ac.OAuth.Google.ClientSecret = secrets.GoogleClientSecret
	}

	if secrets.LinkedInClientID != "" {
		ac.OAuth.LinkedIn.ClientID = secrets.LinkedInClientID
	}
	if secrets.LinkedInClientSecret != "" {
		ac.OAuth.LinkedIn.ClientSecret = secrets.LinkedInClientSecret
	}

	if secrets.Domain != "" {
		ac.Domain = secrets.Domain
	}

	if secrets.FrontendURL != "" {
		ac.FrontendURL = secrets.FrontendURL
	}
}

func (ac *AuthConfig) GetCallbackURL(provider string) string {
	return fmt.Sprintf("http://%s/auth/%s/callback", ac.Domain, provider)
}

func (ac *AuthConfig) GetFrontendRedirectURL(provider, token string) string {
	return fmt.Sprintf("%s/login?token=%s&provider=%s", ac.FrontendURL, token, provider)
}
